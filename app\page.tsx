"use client"

import { useEffect, useState } from "react"
import { motion, useScroll, useTransform } from "framer-motion"

export default function CharacterTransition() {
  const [mounted, setMounted] = useState(false)
  const { scrollYProgress } = useScroll()

  // Transform values based on scroll progress
  const opacity1 = useTransform(scrollYProgress, [0, 0.5], [1, 0])
  const opacity2 = useTransform(scrollYProgress, [0.5, 1], [0, 1])
  const scale1 = useTransform(scrollYProgress, [0, 0.5], [1, 1.2])
  const scale2 = useTransform(scrollYProgress, [0.5, 1], [0.8, 1])
  const rotate = useTransform(scrollYProgress, [0, 1], [0, 360])

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) return null

  return (
    <div className="min-h-[300vh] bg-gradient-to-b from-red-500 via-pink-500 to-red-600">
      {/* Navigation */}
      <nav className="fixed top-0 left-0 right-0 z-50 p-6">
        <div className="flex justify-between items-center text-white font-bold">
          <div className="text-sm tracking-wider">MENU</div>
          <div className="text-sm tracking-wider">ABOUT</div>
        </div>
      </nav>

      {/* Character Transition Container */}
      <div className="sticky top-0 h-screen flex items-center justify-center overflow-hidden">
        {/* Background Text */}
        <div className="absolute inset-0 flex items-center justify-center">
          <motion.div className="text-[12rem] font-black text-white/20 select-none" style={{ rotate }}>
            ODA
          </motion.div>
        </div>

        {/* Character 1 - Modern Oda */}
        <motion.div
          className="absolute inset-0 flex items-center justify-center"
          style={{
            opacity: opacity1,
            scale: scale1,
          }}
        >
          <div className="relative">
            <img src="/images/oda-modern.png" alt="Modern Oda Nobunaga" className="w-96 h-96 object-contain" />
            <motion.div
              className="absolute -top-20 left-1/2 transform -translate-x-1/2"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
            >
              <div className="text-white text-center">
                <div className="text-sm tracking-widest mb-2">織田信長</div>
                <div className="text-6xl font-black tracking-wider">
                  ODA
                  <br />
                  NOBUNAGA
                </div>
              </div>
            </motion.div>
          </div>
        </motion.div>

        {/* Character 2 - Samurai Oda */}
        <motion.div
          className="absolute inset-0 flex items-center justify-center"
          style={{
            opacity: opacity2,
            scale: scale2,
          }}
        >
          <div className="relative">
            <img src="/images/oda-samurai.png" alt="Samurai Oda Nobunaga" className="w-96 h-96 object-contain" />
            <motion.div
              className="absolute -bottom-20 left-1/2 transform -translate-x-1/2"
              style={{ opacity: opacity2 }}
            >
              <div className="text-white text-center">
                <div className="text-6xl font-black tracking-wider mb-2">戦国武将</div>
                <div className="text-sm tracking-widest">SENGOKU WARRIOR</div>
              </div>
            </motion.div>
          </div>
        </motion.div>

        {/* Decorative Elements */}
        <div className="absolute top-10 left-10 text-white text-xs tracking-widest transform -rotate-90 origin-left">
          SCROLL TO TRANSFORM
        </div>
        <div className="absolute top-10 right-10 text-white text-xs tracking-widest transform rotate-90 origin-right">
          CHARACTER EVOLUTION
        </div>
        <div className="absolute bottom-10 left-10 text-white text-xs tracking-widest transform -rotate-90 origin-left">
          MODERN TO TRADITIONAL
        </div>
        <div className="absolute bottom-10 right-10 text-white text-xs tracking-widest transform rotate-90 origin-right">
          NOBUNAGA REIMAGINED
        </div>
      </div>

      {/* Content Sections */}
      <div className="relative z-10 bg-black text-white">
        <section className="min-h-screen flex items-center justify-center p-8">
          <div className="max-w-4xl text-center">
            <h2 className="text-5xl font-black mb-8 tracking-wider">THE TRANSFORMATION</h2>
            <p className="text-xl leading-relaxed opacity-80">
              Experience the evolution of Japan's most legendary warlord through time. From modern interpretation to
              traditional samurai warrior, witness the seamless transition that bridges centuries of history and
              culture.
            </p>
          </div>
        </section>

        <section className="min-h-screen flex items-center justify-center p-8 bg-red-900">
          <div className="max-w-4xl text-center">
            <h2 className="text-5xl font-black mb-8 tracking-wider">NOBUNAGA LEGACY</h2>
            <p className="text-xl leading-relaxed opacity-80">
              The Demon Lord of the Sixth Heaven continues to inspire across generations. His revolutionary spirit
              transcends time, from the Sengoku period to modern digital interpretations.
            </p>
          </div>
        </section>
      </div>
    </div>
  )
}
